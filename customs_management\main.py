#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج تسيير مستخدمي الجمارك الجزائرية
Algerian Customs Personnel Management System

المطور: نظام إدارة الموظفين
التاريخ: 2025
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# إضافة مسار المشروع إلى sys.path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.database_manager import DatabaseManager
from gui.main_window import MainWindow
from utils.config import Config

def main():
    """الدالة الرئيسية لتشغيل البرنامج"""
    try:
        # إنشاء قاعدة البيانات
        db_manager = DatabaseManager()
        db_manager.create_tables()
        
        # إنشاء النافذة الرئيسية
        root = tk.Tk()
        app = MainWindow(root, db_manager)
        
        # تشغيل التطبيق
        root.mainloop()
        
    except Exception as e:
        messagebox.showerror("خطأ", f"حدث خطأ في تشغيل البرنامج:\n{str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
